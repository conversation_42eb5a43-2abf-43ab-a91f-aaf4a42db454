var KLatexEditor = {
    container: null,
    preview: null,
    textarea: null,
    onChangeCallback: null,
    
    init: function(options) {
        this.container = document.getElementById(options.container);
        this.preview = document.getElementById(options.preview);
        this.textarea = document.getElementById('latex-input');
        this.onChangeCallback = options.onChange;
        
        this.initMathJax();
        this.bindEvents();
    },
    
    initMathJax: function() {
        // 初始化MathJax用于公式渲染
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
        
        // 动态加载MathJax
        var script = document.createElement('script');
        script.src = 'https://polyfill.io/v3/polyfill.min.js?features=es6';
        document.head.appendChild(script);
        
        script.onload = function() {
            var mathScript = document.createElement('script');
            mathScript.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js';
            document.head.appendChild(mathScript);
        };
    },
    
    bindEvents: function() {
        var self = this;
        
        // 监听输入变化
        this.textarea.addEventListener('input', function() {
            self.renderFormula();
        });
        
        // 快捷键支持
        this.textarea.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                self.renderFormula();
            }
        });
    },
    
    renderFormula: function() {
        var latex = this.textarea.value;
        
        if (latex.trim()) {
            // 渲染LaTeX公式
            this.preview.innerHTML = '$$' + latex + '$$';
            
            if (window.MathJax) {
                MathJax.typesetPromise([this.preview]).then(() => {
                    // 转换为MathML
                    var mathml = this.latexToMathML(latex);
                    if (this.onChangeCallback) {
                        this.onChangeCallback(latex, mathml);
                    }
                });
            }
        } else {
            this.preview.innerHTML = '';
        }
    },
    
    latexToMathML: function(latex) {
        // 将LaTeX转换为MathML
        // 这里可以使用MathJax的转换功能
        if (window.MathJax && window.MathJax.tex2mml) {
            return MathJax.tex2mml(latex);
        }
        
        // 简单的回退方案
        return '<math><mtext>' + latex + '</mtext></math>';
    },
    
    setContent: function(data) {
        // 设置编辑器内容
        if (data.latex) {
            this.textarea.value = data.latex;
            this.renderFormula();
        }
    },
    
    getContent: function() {
        return {
            latex: this.textarea.value,
            mathml: this.latexToMathML(this.textarea.value)
        };
    }
};