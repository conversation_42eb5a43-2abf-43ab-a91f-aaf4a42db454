(function (window, undefined) {
  var plugin = {
    init: function (data) {
      // 初始化插件
      this.initKLatexEditor();
      this.bindEvents();

      // 如果有初始数据，加载到编辑器
      if (data && data.length > 0) {
        this.loadFormula(data);
      }
    },

    initKLatexEditor: function () {
      // 初始化KLatex编辑器
      KLatexEditor.init({
        container: 'klatex-editor',
        preview: 'formula-preview',
        onChange: this.onFormulaChange.bind(this),
      });
    },

    bindEvents: function () {
      // 绑定按钮事件
      document.getElementById('btn-symbols').onclick = this.showSymbols;
      document.getElementById('btn-functions').onclick = this.showFunctions;
      document.getElementById('btn-matrices').onclick = this.showMatrices;
      document.getElementById('btn-templates').onclick = this.showTemplates;
    },

    onFormulaChange: function (latex, mathml) {
      // 公式变化时的回调
      this.currentLatex = latex;
      this.currentMathML = mathml;
      this.updatePreview();
    },

    updatePreview: function () {
      // 更新预览
      var preview = document.getElementById('formula-preview');
      preview.innerHTML = this.currentMathML;
    },

    getFormula: function () {
      // 获取当前公式数据
      return {
        latex: this.currentLatex,
        mathml: this.currentMathML,
      };
    },

    loadFormula: function (data) {
      // 加载已有公式进行编辑
      KLatexEditor.setContent(data);
    },
  };

  // OnlyOffice插件接口
  window.Asc.plugin.init = function (data) {
    plugin.init(data);
  };

  window.Asc.plugin.button = function (id) {
    if (id === 0) {
      // 确定按钮
      var formula = plugin.getFormula();
      window.Asc.plugin.executeMethod('PasteHtml', [formula.mathml]);
    }
    window.Asc.plugin.executeCommand('close', '');
  };
})(window, undefined);
